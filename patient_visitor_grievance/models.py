from django.db import models
from accounts.models import UserProfile
from base.models import BaseModel
from base.services.permissions.mixins import IncidentsPermissionsMixin
from documents.models import Document
from base.models import Facility
from base.models import Department
from reviews.models import Review
from tasks.models import ReviewProcess, ReviewProcessTasks


class GrievanceInvestigationInvolvedParty(BaseModel):
    # parties involved fields
    name = models.CharField(max_length=255, null=True, blank=True)
    relationship_to_patient = models.CharField(max_length=255, null=True, blank=True)


class GrievanceBase(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    status = models.Char<PERSON>ield(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    date = models.DateField(null=True, blank=True)
    patient_name = models.ForeignKey(
        UserProfile, null=True, blank=True, on_delete=models.SET_NULL
    )
    form_initiated_by = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_form_initiated_by_profile",
    )
    title = models.CharField(max_length=255, null=True, blank=True)
    complaint_made_by = models.ForeignKey(
        UserProfile,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_complaint_made_by_profile",
    )
    relationship_to_patient = models.CharField(max_length=255, null=True, blank=True)
    source_of_information = models.CharField(max_length=255, null=True, blank=True)
    complaint_or_concern = models.TextField(null=True, blank=True)
    initial_corrective_actions = models.TextField(null=True, blank=True)
    adverse_patient_outcome = models.BooleanField(default=False)
    administrator_notified = models.ForeignKey(
        UserProfile,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_administrator_notified",
    )
    notification_date = models.DateField(null=True, blank=True)
    notification_time = models.TimeField(null=True, blank=True)
    outcome = models.TextField(null=True, blank=True)
    reviews = models.ManyToManyField(
        Review, related_name="%(class)s_grievance_reviews_field", blank=True
    )
    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_grievance_incident_documents_field",
        blank=True,
    )

    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    is_resolved = models.BooleanField(default=False)

    def __str__(self):
        return f"Grievance Form - {self.patient_name or 'Unknown'} - {self.date or 'No Date'}"

    class Meta:
        abstract = True
        permissions = IncidentsPermissionsMixin.custom_permissions


class Grievance(GrievanceBase):
    is_modified = models.BooleanField(default=False)
    review_process = models.ForeignKey(
        ReviewProcess,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_review_process",
    )
    review_tasks = models.ManyToManyField(
        ReviewProcessTasks,
        related_name="%(class)s_incident_tasks",
        blank=True,
    )


class GrievanceVersion(GrievanceBase):
    original_report = models.ForeignKey(
        Grievance,
        on_delete=models.CASCADE,
        related_name="versions",
        null=True,
        blank=True,
    )

    class Meta:
        db_table = "grievance_version"


# we need to add search indexes to optimize performance during searching and querying
class GrievanceInvestigation(BaseModel):
    INCIDENT_REVIEW_STATUS_CHOICES = [
        ("Draft", "Draft"),
        ("Open", "Open"),
        ("Pending Assigned", "Pending Assigned"),
        ("Pending Review", "Pending Review"),
        ("Pending Approval", "Pending Approval"),
        ("Resolved", "Resolved"),
    ]
    report_facility = models.ForeignKey(
        Facility,
        blank=True,
        null=True,
        on_delete=models.SET_NULL,
    )
    status = models.CharField(
        choices=INCIDENT_REVIEW_STATUS_CHOICES,
        max_length=255,
        default="Draft",
    )
    current_step = models.IntegerField(default=1, null=True, blank=True)
    FEEDBACK_CHOICES = [
        ("Meeting", "Meeting"),
        ("Telephone", "Telephone"),
    ]
    grievance_report = models.OneToOneField(
        Grievance, blank=True, null=True, on_delete=models.SET_NULL
    )
    findings = models.TextField(max_length=1000, blank=True, null=True)
    interviews_findings = models.TextField(max_length=1000, blank=True, null=True)
    medical_record_findings = models.CharField(max_length=1000, null=True, blank=True)
    conducted_by = models.ForeignKey(
        UserProfile,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="%(class)s_conducted_by",
    )
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    conclusion = models.TextField(null=True, blank=True, max_length=1000)
    action_taken = models.TextField(null=True, blank=True, max_length=1000)
    feedback = models.CharField(
        max_length=100, choices=FEEDBACK_CHOICES, null=True, blank=True
    )
    date_of_feedback = models.DateField(null=True, blank=True)
    involved_parties = models.ManyToManyField(GrievanceInvestigationInvolvedParty)
    # extension fields
    extension_letter_sent = models.BooleanField(null=True, blank=True, default=False)
    date_extension_letter_sent = models.DateField(null=True, blank=True)
    extension_letter_copy = models.ForeignKey(
        Document,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_grievance_extension_letter_sent",
    )

    response_letter_sent = models.BooleanField(null=True, blank=True)
    date_response_letter_sent = models.DateField(null=True, blank=True)
    response_letter_copy = models.ForeignKey(
        Document,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="%(class)s_grievance_response_letter_sent",
    )
    matter_closed = models.BooleanField(null=True, blank=True, default=False)
    date_matter_closed = models.DateField(null=True, blank=True)
    reviews = models.ManyToManyField(
        Review,
        related_name="%(class)s_grievance_investigation_reviews_field",
    )
    documents = models.ManyToManyField(
        Document,
        related_name="%(class)s_grievance_investigation_incident_documents_field",
    )
    department = models.ForeignKey(
        Department, blank=True, null=True, on_delete=models.SET_NULL
    )
    is_resolved = models.BooleanField(default=False)
