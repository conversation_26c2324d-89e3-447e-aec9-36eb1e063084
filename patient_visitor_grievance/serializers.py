from rest_framework import serializers

from accounts.models import Profile, UserProfile
from accounts.serializers import GetProfileSerializer, UserProfileSerializer, UserSerializer
from documents.models import Document
from documents.serializers import DocumentSerializer
from facilities.serializers import FacilitySerializer
from patient_visitor_grievance.models import (
    Grievance,
    GrievanceInvestigation,
    GrievanceInvestigationInvolvedParty,
    GrievanceVersion,
)
from reviews.models import Review


class GrievanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = Grievance
        fields = "__all__"


class GrievanceSerializerVersion(serializers.ModelSerializer):
    class Meta:
        model = GrievanceVersion
        fields = "__all__"

class GrievanceUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = Grievance
        fields = "__all__"

class GrievanceInvestigationUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = GrievanceInvestigation
        fields = "__all__"

class GrievanceListSerializer(serializers.ModelSerializer):
    created_by = UserSerializer()
    updated_by = UserSerializer()
    report_facility = FacilitySerializer()
    patient_name = GetProfileSerializer()
    form_initiated_by = GetProfileSerializer()
    complaint_made_by = GetProfileSerializer()
    administrator_notified = GetProfileSerializer()

    class Meta:
        model = Grievance
        fields = "__all__"


class GrievanceInvestigationInvolvedPartySerializer(serializers.ModelSerializer):
    class Meta:
        model = GrievanceInvestigationInvolvedParty
        fields = "__all__"


class GrievanceInvestigationSerializer(serializers.ModelSerializer):
    documents = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Document.objects.all(), required=False
    )
    involved_parties = serializers.PrimaryKeyRelatedField(
        many=True,
        queryset=GrievanceInvestigationInvolvedParty.objects.all(),
        required=False,
    )
    reviews = serializers.PrimaryKeyRelatedField(
        many=True, queryset=Review.objects.all(), required=False
    )
    conducted_by = serializers.PrimaryKeyRelatedField(
        queryset=UserProfile.objects.all(), allow_null=True, required=False
    )

    class Meta:
        model = GrievanceInvestigation
        fields = "__all__"


class RetrieveGrievanceInvestigationSerializer(serializers.ModelSerializer):
    involved_parties = GrievanceInvestigationInvolvedPartySerializer(many=True)
    extension_letter_copy = DocumentSerializer()
    response_letter_copy = DocumentSerializer()
    conducted_by = UserProfileSerializer()

    class Meta:
        model = GrievanceInvestigation
        fields = "__all__"
