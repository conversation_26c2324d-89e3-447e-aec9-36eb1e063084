from rest_framework import serializers
from base.serializers import BaseModelSerializer, DepartmentSerializer
from facilities.serializers import FacilitySerializer
from workplace_violence_reports.models import WorkPlaceViolence


class GetWorkplaceViolenceSerializer(BaseModelSerializer):
    report_facility = FacilitySerializer()
    department = DepartmentSerializer()
    reported_by = serializers.SerializerMethodField()
    name_of_supervisor = serializers.SerializerMethodField()
    involved_parties = serializers.SerializerMethodField()
    persons_injured = serializers.SerializerMethodField()
    incident_witness = serializers.SerializerMethodField()

    class Meta:
        model = WorkPlaceViolence
        fields = "__all__"

    def get_reported_by(self, obj):
        if obj.reported_by:
            return {
                "id": obj.reported_by.id,
                "first_name": obj.reported_by.first_name,
                "last_name": obj.reported_by.last_name,
                "email": obj.reported_by.email,
            }
        return None

    def get_name_of_supervisor(self, obj):
        if obj.name_of_supervisor:
            return {
                "id": obj.name_of_supervisor.id,
                "first_name": obj.name_of_supervisor.first_name,
                "last_name": obj.name_of_supervisor.last_name,
                "email": obj.name_of_supervisor.email,
            }
        return None

    def get_involved_parties(self, obj):
        return [
            {
                "id": party.id,
                "party_type": party.party_type,
                "title": party.title,
                "assailant_relationship_to_patient": party.assailant_relationship_to_patient,
                "assailant_background": party.assailant_background,
                "user_profile": {
                    "id": party.party.id if party.party else None,
                    "first_name": party.party.first_name if party.party else None,
                    "last_name": party.party.last_name if party.party else None,
                    "email": party.party.email if party.party else None,
                    "profile_type": party.party.profile_type if party.party else None,
                }
            }
            for party in obj.involved_parties.all()
        ]

    def get_persons_injured(self, obj):
        return [
            {
                "id": person.id,
                "injury_description": person.injury_description,
                "user_profile": {
                    "id": person.person.id if person.person else None,
                    "first_name": person.person.first_name if person.person else None,
                    "last_name": person.person.last_name if person.person else None,
                    "email": person.person.email if person.person else None,
                    "profile_type": person.person.profile_type if person.person else None,
                }
            }
            for person in obj.persons_injured.all()
        ]

    def get_incident_witness(self, obj):
        return [
            {
                "id": witness.id,
                "user_profile": {
                    "id": witness.witness.id if witness.witness else None,
                    "first_name": witness.witness.first_name if witness.witness else None,
                    "last_name": witness.witness.last_name if witness.witness else None,
                    "email": witness.witness.email if witness.witness else None,
                    "profile_type": witness.witness.profile_type if witness.witness else None,
                }
            }
            for witness in obj.incident_witness.all()
        ]


