from django.urls import path

from api.views.incidents.grievance_incident.new_grievance import (
    grievance_incident_detail_investigation_api,
    grievance_incident_investigation_api,
    grievance_incidents_api,
    grievance_incident_detail_api,
    grievance_incident_documents_api,
)


urlpatterns = [
    path(
        "",
        grievance_incidents_api,
        name="grievance_incidents_api",
    ),
    path(
        "<int:id>/",
        grievance_incident_detail_api,
        name="grievance_incident_details_api",
    ),
    path(
        "<int:id>/investigation/",
        grievance_incident_investigation_api,
        name="grievance_incident_investigation_api",
    ),
    path(
        "<int:id>/investigation/<int:investigation_id>/",
        grievance_incident_detail_investigation_api,
        name="grievance_incident_details_investigation_api",
    ),
    path(
        "<int:incident_id>/documents/",
        grievance_incident_documents_api,
        name="grievance_incident_documents_api",
    ),
]
