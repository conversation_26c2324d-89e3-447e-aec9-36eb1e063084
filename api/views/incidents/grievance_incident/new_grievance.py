from rest_framework.decorators import api_view, permission_classes, parser_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from base.services.forms import check_user_facility
from patient_visitor_grievance.models import Grievance
from general_patient_visitor.serializers import IncidentListSerializer
from patient_visitor_grievance.services.actions import GrievanceActionsService
from patient_visitor_grievance.services.documents import GrievanceDocumentService
from patient_visitor_grievance.services.operations import (
    GrievanceInvestigationService,
    GrievanceService,
)
from base.services.incidents.get_incidents import GetIncidentsService
from base.services.logging.logger import LoggingService


logging_service = LoggingService()
incidents_service = GetIncidentsService()


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def grievance_incidents_api(request):
    """
    API view for handling Grievance incidents.
    """
    grievance_service = GrievanceService(request.user)
    try:
        if request.method == "GET":

            filters = request.query_params.dict()
            incidents = grievance_service.get_incidents_list(filters=filters)

            if not incidents.success:
                return Response(
                    {"error": incidents.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incidents.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            response = grievance_service.create_incident(request.data)
            if response.success:
                return Response(response.data, status=status.HTTP_201_CREATED)
            else:
                return Response(response.message, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT", "PATCH"])
@permission_classes([IsAuthenticated])
def grievance_incident_detail_api(request, id):
    """API view for handling Grievance incident details."""
    grievance_service = GrievanceService(request.user)
    try:
        grievance_actions_service = GrievanceActionsService(
            user=request.user,
            incident_id=id,
            data=request.data,
        )
        if request.method == "GET":
            incident = grievance_service.get_incident_by_id(incident_id=id)
            if not incident.success:
                return Response(
                    {"error": incident.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            incident = grievance_service.update_incident(
                id=id,
                data=request.data,
            )
            if not incident.success:
                return Response(
                    {"error": incident.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                incident.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PATCH":
            action = request.data.get("action", None)
            if not action:
                return Response(
                    {
                        "error": "Action is required",
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
            if action == "send-for-review":
                response = grievance_actions_service.send_for_review()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "mark-closed":
                response = grievance_actions_service.mark_closed()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "modify":
                response = grievance_actions_service.modify_incident()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            elif action == "delete-draft":
                response = grievance_actions_service.delete_grievance_draft_incidents()
                if not response.success:
                    return Response(
                        {"error": response.message},
                        status=response.code,
                    )
                return Response(
                    response.data,
                    status=status.HTTP_200_OK,
                )
            else:
                return Response(
                    {"error": "Invalid action"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
    except Grievance.DoesNotExist:
        return Response(
            {"error": "Incident not found"},
            status=status.HTTP_404_NOT_FOUND,
        )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def grievance_incident_investigation_api(request, id):
    """
    API view for handling Grievance incident investigations.
    """
    grievance_service = GrievanceInvestigationService(request.user)
    try:
        if request.method == "GET":
            filters = request.query_params.dict()
            investigation = grievance_service.get_investigations_list(
                grievance_id=id, filters=filters
            )
            if not investigation.success:
                return Response(
                    {"error": investigation.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                investigation.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            response = grievance_service.create_investigation(
                grievance_id=id, data=request.data
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "PUT"])
@permission_classes([IsAuthenticated])
def grievance_incident_detail_investigation_api(request, id, investigation_id):
    """
    API view for handling Grievance incident investigation details.
    """
    grievance_service = GrievanceInvestigationService(request.user)
    try:
        if request.method == "GET":
            investigation = grievance_service.get_investigation_by_id(
                grievance_id=id, investigation_id=investigation_id
            )
            if not investigation.success:
                return Response(
                    {"error": investigation.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                investigation.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "PUT":
            response = grievance_service.update_investigation(
                grievance_id=id,
                investigation_id=investigation_id,
                data=request.data,
            )
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=status.HTTP_400_BAD_REQUEST,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )

    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "An error occurred while processing the request."},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )


@api_view(["GET", "POST"])
@permission_classes([IsAuthenticated])
def grievance_incident_documents_api(request, incident_id):
    """
    API view for handling documents related to Grievance incidents.
    """
    try:
        service = GrievanceDocumentService(incident_id=incident_id, user=request.user)
        if request.method == "GET":
            params = request.query_params.dict()
            response = service.get_documents(params=params)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_200_OK,
            )
        elif request.method == "POST":
            files = request.FILES.getlist("files")
            response = service.create_document(files=files)
            if not response.success:
                return Response(
                    {"error": response.message},
                    status=response.code,
                )
            return Response(
                response.data,
                status=status.HTTP_201_CREATED,
            )
        else:
            return Response(
                {"error": "Method not allowed"},
                status=status.HTTP_405_METHOD_NOT_ALLOWED,
            )
    except Exception as e:
        logging_service.log_error(e)
        return Response(
            {"error": "Error processing document request"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )